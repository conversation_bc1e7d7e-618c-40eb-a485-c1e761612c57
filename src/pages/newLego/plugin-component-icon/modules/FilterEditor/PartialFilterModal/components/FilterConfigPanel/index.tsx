import { useCallback } from 'react';
import { Radio, Alert } from '@blmcp/ui';
import { FilterCondition } from '../../index';
import ChartSelectionTable from '../ChartSelectionTable';
import './index.less';

interface FilterConfigPanelProps {
  condition?: FilterCondition;
  onUpdateCondition: (id: string, updates: Partial<FilterCondition>) => void;
  chartComList: any;
}

const FilterConfigPanel = ({
  condition,
  onUpdateCondition,
  chartComList,
}: FilterConfigPanelProps) => {
  // 处理筛选器类型变化
  const handleTypeChange = useCallback(
    (e: any) => {
      if (condition) {
        onUpdateCondition(condition.id, {
          type: e.target.value,
          // 切换类型时清空已选择的图表和字段
          selectedCharts: [],
          selectedFields: {},
        });
      }
    },
    [condition, onUpdateCondition],
  );

  // 处理图表选择变化
  const handleChartSelectionChange = useCallback(
    (selectedCharts: string[], selectedFields: Record<string, string>) => {
      if (condition) {
        onUpdateCondition(condition.id, {
          selectedCharts,
          selectedFields,
        });
      }
    },
    [condition, onUpdateCondition],
  );

  return condition ? (
    <div className="filter-config-panel-context">
      <div className="config-section">
        <div className="section-title">筛选器类型</div>
        <Radio.Group
          value={condition.type}
          onChange={handleTypeChange}
          className="filter-type-radio"
        >
          <Radio value="time">时间筛选器</Radio>
          <Radio value="range">区间筛选器</Radio>
          <Radio value="text">文本筛选器</Radio>
          <Radio value="list">列表筛选器</Radio>
        </Radio.Group>
      </div>

      <div className="config-section-table">
        <div className="section-title">关联图表及字段</div>
        <ChartSelectionTable
          conditionType={condition.type}
          selectedCharts={condition.selectedCharts}
          selectedFields={condition.selectedFields}
          onSelectionChange={handleChartSelectionChange}
          chartComList={chartComList}
        />
      </div>
    </div>
  ) : (
    <div className="filter-config-panel-context">
      <div className="empty-config">
        <div className="empty-config-png"></div>
        <div className="empty-config-text">暂无数据</div>
      </div>
    </div>
  );
};

export default FilterConfigPanel;
